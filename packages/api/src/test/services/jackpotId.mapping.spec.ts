import { expect, should, use } from "chai";
import { suite, test } from "mocha-typescript";
import * as chaiAsPromised from "chai-as-promised";
import { mapJackpotId } from "../../skywind/services/jackpot";
import { EntitySettings } from "../../skywind/entities/settings";

should();
use(chaiAsPromised);

@suite()
class JackpotIdMappingSpec {
    private entitySettings: EntitySettings;

    public before() {
        this.entitySettings = {
            gameProviderSiteCodes: {
                PP: "skywind"
            }
        } as any;
    }
    @test("Internal jackpot id mapping - success")
    public internalJackpotIdMapping() {
        const mappedId = mapJackpotId(this.entitySettings, "SW-SUPER-LION", "EUR");
        expect(mappedId).to.equal("SW-SUPER-LION");
    }

    @test("External jackpot id mapping - success")
    public externalJackpotIdMapping() {
        const externalJackpotId = "PP;jpId;{siteCode}_jackpots-skywind_{currency}";
        const mappedId = mapJackpotId(this.entitySettings, externalJackpotId, "EUR");
        expect(mappedId).to.equal("PP;jpId;skywind_jackpots-skywind_EUR");
    }

    @test("External jackpot id mapping - does nothing because of malformed external jackpot id template")
    public externalJackpotIdMappingDoesNothingBecauseTemplateIsMalformed() {
        const externalJackpotId = "PP;{siteCode}_jackpots-skywind_{currency}";
        const mappedId = mapJackpotId(this.entitySettings, externalJackpotId, "EUR");
        expect(mappedId).to.equal("PP;{siteCode}_jackpots-skywind_{currency}");
    }

    @test("External jackpot id mapping - does not replace siteCode because of unknown game provider code")
    public externalJackpotIdMappingDoesNotReplaceSiteCodeBecauseOfUnknownGameProviderCode() {
        const externalJackpotId = "SS;jpId;{siteCode}_jackpots-skywind_{currency}";
        const mappedId = mapJackpotId(this.entitySettings, externalJackpotId, "EUR");
        expect(mappedId).to.equal("SS;jpId;{siteCode}_jackpots-skywind_EUR");
    }
}
